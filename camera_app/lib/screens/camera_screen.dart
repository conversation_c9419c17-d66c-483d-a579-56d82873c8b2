import 'package:flutter/material.dart';
import '../services/http_server.dart';
import '../services/camera_service.dart';
import '../services/network_service.dart';

class CameraScreen extends StatefulWidget {
  const CameraScreen({super.key});

  @override
  State<CameraScreen> createState() => _CameraScreenState();
}

class _CameraScreenState extends State<CameraScreen> {
  final HttpServerService _httpServer = HttpServerService();
  final CameraService _cameraService = CameraService();
  
  RecordingStatus _currentStatus = RecordingStatus.standby;
  String _networkInfo = 'Loading...';
  String _errorMessage = '';
  bool _isInitializing = true;

  @override
  void initState() {
    super.initState();
    _initializeServices();
  }

  Future<void> _initializeServices() async {
    try {
      // Initialize camera service
      final cameraInitialized = await _cameraService.initializeCamera();
      if (!cameraInitialized) {
        setState(() {
          _errorMessage = 'Failed to initialize camera';
          _isInitializing = false;
        });
        return;
      }

      // Get network information
      final networkInfo = await NetworkService.getNetworkInfo();
      setState(() {
        _networkInfo = networkInfo;
      });

      // Set up HTTP server callbacks
      _httpServer.onStartRecord = _startRecording;
      _httpServer.onStopRecord = _stopRecording;
      _httpServer.onStatusChanged = _updateStatus;

      // Start HTTP server
      await _httpServer.startServer();

      setState(() {
        _isInitializing = false;
      });

      print('All services initialized successfully');
    } catch (e) {
      setState(() {
        _errorMessage = 'Initialization failed: $e';
        _isInitializing = false;
      });
      print('Error initializing services: $e');
    }
  }

  void _startRecording() async {
    try {
      final success = await _cameraService.startRecording();
      if (!success) {
        setState(() {
          _errorMessage = 'Failed to start recording';
        });
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Recording error: $e';
      });
    }
  }

  void _stopRecording() async {
    try {
      final videoPath = await _cameraService.stopRecording();
      if (videoPath == null) {
        setState(() {
          _errorMessage = 'Failed to stop recording';
        });
      } else {
        print('Video saved: $videoPath');
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Stop recording error: $e';
      });
    }
  }

  void _updateStatus(RecordingStatus status) {
    setState(() {
      _currentStatus = status;
      if (status != RecordingStatus.standby) {
        _errorMessage = ''; // Clear error when status changes
      }
    });
  }

  String _getStatusText() {
    switch (_currentStatus) {
      case RecordingStatus.standby:
        return 'STANDBY';
      case RecordingStatus.recording:
        return 'RECORDING';
      case RecordingStatus.saved:
        return 'SAVED';
    }
  }

  Color _getStatusColor() {
    switch (_currentStatus) {
      case RecordingStatus.standby:
        return Colors.blue;
      case RecordingStatus.recording:
        return Colors.red;
      case RecordingStatus.saved:
        return Colors.green;
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isInitializing) {
      return Scaffold(
        backgroundColor: Colors.black,
        body: const Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              CircularProgressIndicator(color: Colors.white),
              SizedBox(height: 20),
              Text(
                'Initializing Camera...',
                style: TextStyle(color: Colors.white, fontSize: 18),
              ),
            ],
          ),
        ),
      );
    }

    if (_errorMessage.isNotEmpty) {
      return Scaffold(
        backgroundColor: Colors.black,
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(
                Icons.error_outline,
                color: Colors.red,
                size: 64,
              ),
              const SizedBox(height: 20),
              Text(
                'Error',
                style: const TextStyle(
                  color: Colors.red,
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 10),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 20),
                child: Text(
                  _errorMessage,
                  style: const TextStyle(color: Colors.white, fontSize: 16),
                  textAlign: TextAlign.center,
                ),
              ),
              const SizedBox(height: 30),
              ElevatedButton(
                onPressed: () {
                  setState(() {
                    _errorMessage = '';
                    _isInitializing = true;
                  });
                  _initializeServices();
                },
                child: const Text('Retry'),
              ),
            ],
          ),
        ),
      );
    }

    return Scaffold(
      backgroundColor: Colors.black,
      body: SafeArea(
        child: Column(
          children: [
            // Camera Preview (small, top area)
            Container(
              height: 200,
              width: double.infinity,
              margin: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                border: Border.all(color: Colors.white, width: 2),
                borderRadius: BorderRadius.circular(8),
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(6),
                child: _cameraService.getCameraPreview() ?? 
                  const Center(
                    child: Text(
                      'Camera Preview',
                      style: TextStyle(color: Colors.white),
                    ),
                  ),
              ),
            ),
            
            // Main Status Display (center, large)
            Expanded(
              child: Center(
                child: Text(
                  _getStatusText(),
                  style: TextStyle(
                    color: _getStatusColor(),
                    fontSize: 48,
                    fontWeight: FontWeight.bold,
                    letterSpacing: 4,
                  ),
                ),
              ),
            ),
            
            // Network Info (bottom)
            Container(
              padding: const EdgeInsets.all(16),
              child: Column(
                children: [
                  Text(
                    'Network Address:',
                    style: const TextStyle(
                      color: Colors.white70,
                      fontSize: 14,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    _networkInfo,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      fontFamily: 'monospace',
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  void dispose() {
    _httpServer.stopServer();
    _cameraService.dispose();
    super.dispose();
  }
}
