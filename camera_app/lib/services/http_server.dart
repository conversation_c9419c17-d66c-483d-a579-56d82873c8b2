import 'dart:io';
import 'dart:convert';
import 'package:shelf/shelf.dart';
import 'package:shelf/shelf_io.dart';
import 'package:shelf_router/shelf_router.dart';

enum RecordingStatus { standby, recording, saved }

class HttpServerService {
  static const int port = 8080;
  HttpServer? _server;
  RecordingStatus _status = RecordingStatus.standby;
  
  // Callback functions to be set by the UI
  Function()? onStartRecord;
  Function()? onStopRecord;
  Function(RecordingStatus)? onStatusChanged;

  RecordingStatus get status => _status;

  Future<void> startServer() async {
    final router = Router();

    // CORS headers for all responses
    Response _corsHeaders(Response response) => response.change(headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Origin, Content-Type',
    });

    // Handle preflight requests
    router.options('/<ignored|.*>', (Request request) {
      return Response.ok('', headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
        'Access-Control-Allow-Headers': 'Origin, Content-Type',
      });
    });

    // Start recording endpoint
    router.get('/start_record', (Request request) async {
      try {
        if (_status == RecordingStatus.recording) {
          return _corsHeaders(Response.ok(
            jsonEncode({'success': false, 'message': 'Already recording'}),
            headers: {'Content-Type': 'application/json'},
          ));
        }

        _updateStatus(RecordingStatus.recording);
        
        // Trigger recording start
        if (onStartRecord != null) {
          onStartRecord!();
        }

        return _corsHeaders(Response.ok(
          jsonEncode({'success': true, 'status': 'recording'}),
          headers: {'Content-Type': 'application/json'},
        ));
      } catch (e) {
        return _corsHeaders(Response.internalServerError(
          body: jsonEncode({'success': false, 'error': e.toString()}),
          headers: {'Content-Type': 'application/json'},
        ));
      }
    });

    // Stop recording endpoint
    router.get('/stop_record', (Request request) async {
      try {
        if (_status != RecordingStatus.recording) {
          return _corsHeaders(Response.ok(
            jsonEncode({'success': false, 'message': 'Not currently recording'}),
            headers: {'Content-Type': 'application/json'},
          ));
        }

        _updateStatus(RecordingStatus.saved);
        
        // Trigger recording stop
        if (onStopRecord != null) {
          onStopRecord!();
        }

        // Auto-revert to standby after 3 seconds
        Future.delayed(const Duration(seconds: 3), () {
          _updateStatus(RecordingStatus.standby);
        });

        return _corsHeaders(Response.ok(
          jsonEncode({'success': true, 'status': 'saved'}),
          headers: {'Content-Type': 'application/json'},
        ));
      } catch (e) {
        return _corsHeaders(Response.internalServerError(
          body: jsonEncode({'success': false, 'error': e.toString()}),
          headers: {'Content-Type': 'application/json'},
        ));
      }
    });

    // Status endpoint
    router.get('/status', (Request request) {
      return _corsHeaders(Response.ok(
        jsonEncode({'status': _status.name}),
        headers: {'Content-Type': 'application/json'},
      ));
    });

    // Health check endpoint
    router.get('/health', (Request request) {
      return _corsHeaders(Response.ok(
        jsonEncode({'status': 'healthy', 'timestamp': DateTime.now().toIso8601String()}),
        headers: {'Content-Type': 'application/json'},
      ));
    });

    // Default handler for unknown routes
    router.all('/<ignored|.*>', (Request request) {
      return _corsHeaders(Response.notFound(
        jsonEncode({'error': 'Endpoint not found'}),
        headers: {'Content-Type': 'application/json'},
      ));
    });

    try {
      _server = await serve(router, InternetAddress.anyIPv4, port);
      print('HTTP Server started on port $port');
    } catch (e) {
      print('Failed to start HTTP server: $e');
      rethrow;
    }
  }

  void _updateStatus(RecordingStatus newStatus) {
    _status = newStatus;
    if (onStatusChanged != null) {
      onStatusChanged!(newStatus);
    }
  }

  Future<void> stopServer() async {
    await _server?.close();
    _server = null;
    print('HTTP Server stopped');
  }

  String get serverUrl {
    return 'http://0.0.0.0:$port';
  }
}
