import 'dart:io';
import 'package:network_info_plus/network_info_plus.dart';

class NetworkService {
  static const int serverPort = 8080;
  
  static Future<String> getDeviceIP() async {
    try {
      final info = NetworkInfo();
      final wifiIP = await info.getWifiIP();
      
      if (wifiIP != null && wifiIP.isNotEmpty) {
        return wifiIP;
      }
      
      // Fallback method using NetworkInterface
      for (var interface in await NetworkInterface.list()) {
        for (var addr in interface.addresses) {
          if (addr.type == InternetAddressType.IPv4 && 
              !addr.isLoopback && 
              addr.address.startsWith('192.168.')) {
            return addr.address;
          }
        }
      }
      
      return 'Unknown';
    } catch (e) {
      print('Error getting device IP: $e');
      return 'Error';
    }
  }
  
  static Future<String> getNetworkInfo() async {
    final ip = await getDeviceIP();
    return '$ip:$serverPort';
  }
  
  static Future<bool> isConnectedToWifi() async {
    try {
      final info = NetworkInfo();
      final wifiName = await info.getWifiName();
      return wifiName != null && wifiName.isNotEmpty;
    } catch (e) {
      print('Error checking WiFi connection: $e');
      return false;
    }
  }
}
