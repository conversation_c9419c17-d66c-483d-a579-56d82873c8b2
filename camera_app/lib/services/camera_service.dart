import 'dart:io';
import 'package:flutter/material.dart';
import 'package:camera/camera.dart';
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';

class CameraService {
  CameraController? _controller;
  bool _isInitialized = false;
  bool _isRecording = false;
  String? _currentVideoPath;

  CameraController? get controller => _controller;
  bool get isInitialized => _isInitialized;
  bool get isRecording => _isRecording;

  Future<bool> requestPermissions() async {
    final permissions = [
      Permission.camera,
      Permission.microphone,
      Permission.storage,
    ];

    Map<Permission, PermissionStatus> statuses = await permissions.request();
    
    bool allGranted = true;
    for (var permission in permissions) {
      if (statuses[permission] != PermissionStatus.granted) {
        allGranted = false;
        print('Permission ${permission.toString()} not granted');
      }
    }
    
    return allGranted;
  }

  Future<bool> initializeCamera() async {
    try {
      // Request permissions first
      if (!await requestPermissions()) {
        print('Camera permissions not granted');
        return false;
      }

      // Get available cameras
      final cameras = await availableCameras();
      if (cameras.isEmpty) {
        print('No cameras available');
        return false;
      }

      // Use the first rear camera (usually index 0)
      final camera = cameras.firstWhere(
        (camera) => camera.lensDirection == CameraLensDirection.back,
        orElse: () => cameras.first,
      );

      // Initialize camera controller
      _controller = CameraController(
        camera,
        ResolutionPreset.high,
        enableAudio: true,
      );

      await _controller!.initialize();
      _isInitialized = true;
      
      print('Camera initialized successfully');
      return true;
    } catch (e) {
      print('Error initializing camera: $e');
      _isInitialized = false;
      return false;
    }
  }

  Future<bool> startRecording() async {
    if (!_isInitialized || _controller == null) {
      print('Camera not initialized');
      return false;
    }

    if (_isRecording) {
      print('Already recording');
      return false;
    }

    try {
      // Create video file path
      final directory = await getApplicationDocumentsDirectory();
      final videoDirectory = Directory('${directory.path}/videos');
      if (!await videoDirectory.exists()) {
        await videoDirectory.create(recursive: true);
      }

      final timestamp = DateTime.now().millisecondsSinceEpoch;
      _currentVideoPath = '${videoDirectory.path}/video_$timestamp.mp4';

      // Start recording
      await _controller!.startVideoRecording();
      _isRecording = true;
      
      print('Recording started: $_currentVideoPath');
      return true;
    } catch (e) {
      print('Error starting recording: $e');
      _isRecording = false;
      return false;
    }
  }

  Future<String?> stopRecording() async {
    if (!_isRecording || _controller == null) {
      print('Not currently recording');
      return null;
    }

    try {
      final videoFile = await _controller!.stopVideoRecording();
      _isRecording = false;

      // Move video to permanent location and save to gallery
      if (_currentVideoPath != null) {
        final permanentFile = File(_currentVideoPath!);
        await videoFile.saveTo(_currentVideoPath!);
        
        // Try to save to device gallery/DCIM
        await _saveToGallery(permanentFile);
        
        print('Recording stopped and saved: $_currentVideoPath');
        return _currentVideoPath;
      }
      
      return videoFile.path;
    } catch (e) {
      print('Error stopping recording: $e');
      _isRecording = false;
      return null;
    }
  }

  Future<void> _saveToGallery(File videoFile) async {
    try {
      // For Android, try to save to DCIM folder
      if (Platform.isAndroid) {
        final dcimDirectory = Directory('/storage/emulated/0/DCIM/CameraApp');
        if (!await dcimDirectory.exists()) {
          await dcimDirectory.create(recursive: true);
        }
        
        final fileName = videoFile.path.split('/').last;
        final galleryFile = File('${dcimDirectory.path}/$fileName');
        await videoFile.copy(galleryFile.path);
        
        print('Video saved to gallery: ${galleryFile.path}');
      }
      // For iOS, the video is automatically saved to the app's documents directory
      // Additional iOS gallery integration would require platform-specific code
    } catch (e) {
      print('Error saving to gallery: $e');
      // Continue even if gallery save fails - video is still saved in app directory
    }
  }

  void dispose() {
    _controller?.dispose();
    _controller = null;
    _isInitialized = false;
    _isRecording = false;
  }

  // Get preview widget for UI
  Widget? getCameraPreview() {
    if (_isInitialized && _controller != null) {
      return CameraPreview(_controller!);
    }
    return null;
  }
}
