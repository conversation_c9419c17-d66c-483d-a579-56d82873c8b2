import 'dart:async';
import 'dart:convert';
import 'package:http/http.dart' as http;
import '../models/camera_device.dart';
import 'camera_manager.dart';

class StatusMonitor {
  final CameraManager _cameraManager;
  Timer? _monitoringTimer;
  static const Duration _pollingInterval = Duration(seconds: 5);
  static const Duration _requestTimeout = Duration(seconds: 3);
  
  bool _isMonitoring = false;
  Function()? onStatusUpdate;

  StatusMonitor(this._cameraManager);

  bool get isMonitoring => _isMonitoring;

  // Start monitoring all cameras
  void startMonitoring() {
    if (_isMonitoring) {
      return;
    }

    _isMonitoring = true;
    _monitoringTimer = Timer.periodic(_pollingInterval, (_) {
      _checkAllCameraStatuses();
    });

    // Initial check
    _checkAllCameraStatuses();
    
    print('Status monitoring started');
  }

  // Stop monitoring
  void stopMonitoring() {
    _monitoringTimer?.cancel();
    _monitoringTimer = null;
    _isMonitoring = false;
    
    print('Status monitoring stopped');
  }

  // Check status of all cameras
  Future<void> _checkAllCameraStatuses() async {
    final cameras = _cameraManager.cameras.toList();
    
    if (cameras.isEmpty) {
      return;
    }

    // Create futures for parallel status checks
    final futures = cameras.map((camera) => _checkCameraStatus(camera)).toList();
    
    try {
      // Execute all status checks in parallel
      await Future.wait(futures);
      
      // Notify UI of updates
      if (onStatusUpdate != null) {
        onStatusUpdate!();
      }
    } catch (e) {
      print('Error during status monitoring: $e');
    }
  }

  // Check status of a single camera
  Future<void> _checkCameraStatus(CameraDevice camera) async {
    try {
      final url = Uri.parse('${camera.baseUrl}/status');
      final response = await http.get(url).timeout(_requestTimeout);

      if (response.statusCode == 200) {
        final responseData = jsonDecode(response.body);
        final statusString = responseData['status'] as String?;
        
        CameraStatus newStatus;
        switch (statusString?.toLowerCase()) {
          case 'recording':
            newStatus = CameraStatus.recording;
            break;
          case 'standby':
          case 'saved':
            newStatus = CameraStatus.connected;
            break;
          default:
            newStatus = CameraStatus.connected;
        }

        _cameraManager.updateCameraStatus(camera.id, newStatus);
      } else {
        // HTTP error - mark as offline
        _cameraManager.updateCameraStatus(
          camera.id, 
          CameraStatus.offline,
          error: 'HTTP ${response.statusCode}',
        );
      }
    } catch (e) {
      // Network error or timeout - mark as offline
      _cameraManager.updateCameraStatus(
        camera.id, 
        CameraStatus.offline,
        error: 'Connection failed: ${e.toString()}',
      );
    }
  }

  // Perform a one-time status check for all cameras
  Future<void> refreshAllStatuses() async {
    await _checkAllCameraStatuses();
  }

  // Perform a one-time status check for a specific camera
  Future<void> refreshCameraStatus(String cameraId) async {
    final camera = _cameraManager.getCameraById(cameraId);
    if (camera != null) {
      await _checkCameraStatus(camera);
      
      if (onStatusUpdate != null) {
        onStatusUpdate!();
      }
    }
  }

  // Test connection to a camera (used when adding new cameras)
  Future<bool> testCameraConnection(String ipAddress, {int port = 8080}) async {
    try {
      final url = Uri.parse('http://$ipAddress:$port/health');
      final response = await http.get(url).timeout(_requestTimeout);
      
      return response.statusCode == 200;
    } catch (e) {
      // Try the status endpoint as fallback
      try {
        final url = Uri.parse('http://$ipAddress:$port/status');
        final response = await http.get(url).timeout(_requestTimeout);
        
        return response.statusCode == 200;
      } catch (e) {
        return false;
      }
    }
  }

  // Get summary of camera statuses
  Map<String, int> getStatusSummary() {
    final cameras = _cameraManager.cameras;
    
    return {
      'total': cameras.length,
      'connected': cameras.where((c) => c.status == CameraStatus.connected).length,
      'recording': cameras.where((c) => c.status == CameraStatus.recording).length,
      'offline': cameras.where((c) => c.status == CameraStatus.offline).length,
    };
  }

  // Check if any cameras are currently recording
  bool get hasRecordingCameras {
    return _cameraManager.cameras.any((c) => c.status == CameraStatus.recording);
  }

  // Check if any cameras are connected
  bool get hasConnectedCameras {
    return _cameraManager.cameras.any((c) => c.status == CameraStatus.connected);
  }

  // Get cameras that haven't been seen recently
  List<CameraDevice> getStaleConnections({Duration threshold = const Duration(minutes: 2)}) {
    final cutoff = DateTime.now().subtract(threshold);
    return _cameraManager.cameras
        .where((c) => c.lastSeen.isBefore(cutoff))
        .toList();
  }

  void dispose() {
    stopMonitoring();
  }
}
