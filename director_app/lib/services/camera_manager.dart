import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/camera_device.dart';

class CameraManager {
  static const String _storageKey = 'camera_devices';
  List<CameraDevice> _cameras = [];
  
  List<CameraDevice> get cameras => List.unmodifiable(_cameras);

  // Load cameras from persistent storage
  Future<void> loadCameras() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final camerasJson = prefs.getString(_storageKey);
      
      if (camerasJson != null) {
        final List<dynamic> camerasList = jsonDecode(camerasJson);
        _cameras = camerasList
            .map((json) => CameraDevice.fromJson(json))
            .toList();
      }
    } catch (e) {
      print('Error loading cameras: $e');
      _cameras = [];
    }
  }

  // Save cameras to persistent storage
  Future<void> saveCameras() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final camerasJson = jsonEncode(_cameras.map((c) => c.toJson()).toList());
      await prefs.setString(_storageKey, camerasJson);
    } catch (e) {
      print('Error saving cameras: $e');
    }
  }

  // Add a new camera
  Future<bool> addCamera(String ipAddress, {int port = 8080}) async {
    if (!_isValidIP(ipAddress)) {
      return false;
    }

    final id = _generateCameraId(ipAddress, port);
    
    // Check if camera already exists
    if (_cameras.any((c) => c.id == id)) {
      return false;
    }

    final camera = CameraDevice(
      id: id,
      ipAddress: ipAddress,
      port: port,
    );

    _cameras.add(camera);
    await saveCameras();
    return true;
  }

  // Remove a camera
  Future<bool> removeCamera(String cameraId) async {
    final initialLength = _cameras.length;
    _cameras.removeWhere((c) => c.id == cameraId);
    
    if (_cameras.length < initialLength) {
      await saveCameras();
      return true;
    }
    return false;
  }

  // Update camera status
  void updateCameraStatus(String cameraId, CameraStatus status, {String? error}) {
    final index = _cameras.indexWhere((c) => c.id == cameraId);
    if (index != -1) {
      _cameras[index] = _cameras[index].copyWith(
        status: status,
        lastSeen: DateTime.now(),
        lastError: error,
      );
    }
  }

  // Get camera by ID
  CameraDevice? getCameraById(String cameraId) {
    try {
      return _cameras.firstWhere((c) => c.id == cameraId);
    } catch (e) {
      return null;
    }
  }

  // Get cameras by status
  List<CameraDevice> getCamerasByStatus(CameraStatus status) {
    return _cameras.where((c) => c.status == status).toList();
  }

  // Get connected cameras count
  int get connectedCamerasCount => 
      _cameras.where((c) => c.status == CameraStatus.connected).length;

  // Get recording cameras count
  int get recordingCamerasCount => 
      _cameras.where((c) => c.status == CameraStatus.recording).length;

  // Clear all cameras
  Future<void> clearAllCameras() async {
    _cameras.clear();
    await saveCameras();
  }

  // Validate IP address format
  bool _isValidIP(String ip) {
    final ipRegex = RegExp(
      r'^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$'
    );
    return ipRegex.hasMatch(ip);
  }

  // Generate unique camera ID
  String _generateCameraId(String ipAddress, int port) {
    return '${ipAddress}_$port';
  }

  // Parse IP address with port from string (e.g., "*************:8080")
  static Map<String, dynamic>? parseAddress(String address) {
    try {
      final parts = address.split(':');
      if (parts.length == 2) {
        final ip = parts[0];
        final port = int.parse(parts[1]);
        return {'ip': ip, 'port': port};
      } else if (parts.length == 1) {
        // Default port if not specified
        return {'ip': parts[0], 'port': 8080};
      }
    } catch (e) {
      print('Error parsing address: $e');
    }
    return null;
  }

  // Validate full address format (IP:PORT)
  static bool isValidAddress(String address) {
    final parsed = parseAddress(address);
    if (parsed == null) return false;
    
    final ip = parsed['ip'] as String;
    final port = parsed['port'] as int;
    
    // Validate IP
    final ipRegex = RegExp(
      r'^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$'
    );
    
    // Validate port range
    return ipRegex.hasMatch(ip) && port > 0 && port <= 65535;
  }
}
