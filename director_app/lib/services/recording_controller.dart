import 'dart:convert';
import 'package:http/http.dart' as http;
import '../models/camera_device.dart';
import 'camera_manager.dart';

class RecordingResult {
  final String cameraId;
  final bool success;
  final String? error;
  final Map<String, dynamic>? response;

  RecordingResult({
    required this.cameraId,
    required this.success,
    this.error,
    this.response,
  });
}

class RecordingController {
  final CameraManager _cameraManager;
  static const Duration _requestTimeout = Duration(seconds: 3);
  
  bool _isOperationInProgress = false;

  RecordingController(this._cameraManager);

  bool get isOperationInProgress => _isOperationInProgress;

  // Start recording on all connected cameras
  Future<List<RecordingResult>> startAllRecording() async {
    if (_isOperationInProgress) {
      throw Exception('Another operation is already in progress');
    }

    _isOperationInProgress = true;
    
    try {
      final connectedCameras = _cameraManager.cameras
          .where((c) => c.status == CameraStatus.connected)
          .toList();

      if (connectedCameras.isEmpty) {
        return [];
      }

      // Create futures for parallel execution
      final futures = connectedCameras.map((camera) => 
          _sendStartCommand(camera)).toList();

      // Execute all requests in parallel
      final results = await Future.wait(futures);

      // Update camera statuses based on results
      for (final result in results) {
        if (result.success) {
          _cameraManager.updateCameraStatus(result.cameraId, CameraStatus.recording);
        } else {
          _cameraManager.updateCameraStatus(
            result.cameraId, 
            CameraStatus.connected, 
            error: result.error,
          );
        }
      }

      return results;
    } finally {
      _isOperationInProgress = false;
    }
  }

  // Stop recording on all recording cameras
  Future<List<RecordingResult>> stopAllRecording() async {
    if (_isOperationInProgress) {
      throw Exception('Another operation is already in progress');
    }

    _isOperationInProgress = true;
    
    try {
      final recordingCameras = _cameraManager.cameras
          .where((c) => c.status == CameraStatus.recording)
          .toList();

      if (recordingCameras.isEmpty) {
        return [];
      }

      // Create futures for parallel execution
      final futures = recordingCameras.map((camera) => 
          _sendStopCommand(camera)).toList();

      // Execute all requests in parallel
      final results = await Future.wait(futures);

      // Update camera statuses based on results
      for (final result in results) {
        if (result.success) {
          _cameraManager.updateCameraStatus(result.cameraId, CameraStatus.connected);
        } else {
          _cameraManager.updateCameraStatus(
            result.cameraId, 
            CameraStatus.recording, 
            error: result.error,
          );
        }
      }

      return results;
    } finally {
      _isOperationInProgress = false;
    }
  }

  // Send start recording command to a single camera
  Future<RecordingResult> _sendStartCommand(CameraDevice camera) async {
    try {
      final url = Uri.parse('${camera.baseUrl}/start_record');
      final response = await http.get(url).timeout(_requestTimeout);

      if (response.statusCode == 200) {
        final responseData = jsonDecode(response.body);
        return RecordingResult(
          cameraId: camera.id,
          success: responseData['success'] ?? false,
          response: responseData,
          error: responseData['success'] == false ? responseData['message'] : null,
        );
      } else {
        return RecordingResult(
          cameraId: camera.id,
          success: false,
          error: 'HTTP ${response.statusCode}: ${response.reasonPhrase}',
        );
      }
    } catch (e) {
      return RecordingResult(
        cameraId: camera.id,
        success: false,
        error: e.toString(),
      );
    }
  }

  // Send stop recording command to a single camera
  Future<RecordingResult> _sendStopCommand(CameraDevice camera) async {
    try {
      final url = Uri.parse('${camera.baseUrl}/stop_record');
      final response = await http.get(url).timeout(_requestTimeout);

      if (response.statusCode == 200) {
        final responseData = jsonDecode(response.body);
        return RecordingResult(
          cameraId: camera.id,
          success: responseData['success'] ?? false,
          response: responseData,
          error: responseData['success'] == false ? responseData['message'] : null,
        );
      } else {
        return RecordingResult(
          cameraId: camera.id,
          success: false,
          error: 'HTTP ${response.statusCode}: ${response.reasonPhrase}',
        );
      }
    } catch (e) {
      return RecordingResult(
        cameraId: camera.id,
        success: false,
        error: e.toString(),
      );
    }
  }

  // Emergency stop - attempt to stop all cameras regardless of current status
  Future<List<RecordingResult>> emergencyStopAll() async {
    if (_isOperationInProgress) {
      throw Exception('Another operation is already in progress');
    }

    _isOperationInProgress = true;
    
    try {
      final allCameras = _cameraManager.cameras.toList();

      if (allCameras.isEmpty) {
        return [];
      }

      // Create futures for parallel execution
      final futures = allCameras.map((camera) => 
          _sendStopCommand(camera)).toList();

      // Execute all requests in parallel
      final results = await Future.wait(futures);

      // Update camera statuses based on results
      for (final result in results) {
        if (result.success) {
          _cameraManager.updateCameraStatus(result.cameraId, CameraStatus.connected);
        } else {
          _cameraManager.updateCameraStatus(
            result.cameraId, 
            CameraStatus.offline, 
            error: result.error,
          );
        }
      }

      return results;
    } finally {
      _isOperationInProgress = false;
    }
  }
}
