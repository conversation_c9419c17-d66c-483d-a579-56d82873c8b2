enum CameraStatus { connected, recording, offline }

class CameraDevice {
  final String id;
  final String ipAddress;
  final int port;
  CameraStatus status;
  DateTime lastSeen;
  String? lastError;

  CameraDevice({
    required this.id,
    required this.ipAddress,
    this.port = 8080,
    this.status = CameraStatus.offline,
    DateTime? lastSeen,
    this.lastError,
  }) : lastSeen = lastSeen ?? DateTime.now();

  String get fullAddress => '$ipAddress:$port';
  String get baseUrl => 'http://$ipAddress:$port';

  // Convert to JSON for storage
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'ipAddress': ipAddress,
      'port': port,
      'status': status.name,
      'lastSeen': lastSeen.toIso8601String(),
      'lastError': lastError,
    };
  }

  // Create from JSON
  factory CameraDevice.fromJson(Map<String, dynamic> json) {
    return CameraDevice(
      id: json['id'],
      ipAddress: json['ipAddress'],
      port: json['port'] ?? 8080,
      status: CameraStatus.values.firstWhere(
        (e) => e.name == json['status'],
        orElse: () => CameraStatus.offline,
      ),
      lastSeen: DateTime.parse(json['lastSeen']),
      lastError: json['lastError'],
    );
  }

  // Create a copy with updated fields
  CameraDevice copyWith({
    String? id,
    String? ipAddress,
    int? port,
    CameraStatus? status,
    DateTime? lastSeen,
    String? lastError,
  }) {
    return CameraDevice(
      id: id ?? this.id,
      ipAddress: ipAddress ?? this.ipAddress,
      port: port ?? this.port,
      status: status ?? this.status,
      lastSeen: lastSeen ?? this.lastSeen,
      lastError: lastError ?? this.lastError,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is CameraDevice && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'CameraDevice(id: $id, address: $fullAddress, status: ${status.name})';
  }
}
