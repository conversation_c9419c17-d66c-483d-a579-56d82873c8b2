import 'package:flutter/material.dart';
import '../models/camera_device.dart';
import '../services/camera_manager.dart';
import '../services/recording_controller.dart';
import '../services/status_monitor.dart';
import '../widgets/camera_list_item.dart';
import '../widgets/add_camera_dialog.dart';

class DirectorScreen extends StatefulWidget {
  const DirectorScreen({super.key});

  @override
  State<DirectorScreen> createState() => _DirectorScreenState();
}

class _DirectorScreenState extends State<DirectorScreen> {
  final CameraManager _cameraManager = CameraManager();
  late final RecordingController _recordingController;
  late final StatusMonitor _statusMonitor;
  
  bool _isInitialized = false;
  bool _isOperationInProgress = false;
  String? _lastOperationResult;

  @override
  void initState() {
    super.initState();
    _recordingController = RecordingController(_cameraManager);
    _statusMonitor = StatusMonitor(_cameraManager);
    
    _statusMonitor.onStatusUpdate = () {
      if (mounted) {
        setState(() {});
      }
    };
    
    _initializeServices();
  }

  Future<void> _initializeServices() async {
    try {
      await _cameraManager.loadCameras();
      _statusMonitor.startMonitoring();
      
      setState(() {
        _isInitialized = true;
      });
    } catch (e) {
      print('Error initializing services: $e');
      setState(() {
        _isInitialized = true;
      });
    }
  }

  Future<void> _startAllRecording() async {
    if (_isOperationInProgress) return;

    setState(() {
      _isOperationInProgress = true;
      _lastOperationResult = null;
    });

    try {
      final results = await _recordingController.startAllRecording();
      final successCount = results.where((r) => r.success).length;
      final totalCount = results.length;
      
      setState(() {
        _lastOperationResult = 'Started recording on $successCount/$totalCount cameras';
      });
      
      if (successCount < totalCount) {
        _showOperationResults('Start Recording Results', results);
      }
    } catch (e) {
      setState(() {
        _lastOperationResult = 'Error: ${e.toString()}';
      });
    } finally {
      setState(() {
        _isOperationInProgress = false;
      });
    }
  }

  Future<void> _stopAllRecording() async {
    if (_isOperationInProgress) return;

    setState(() {
      _isOperationInProgress = true;
      _lastOperationResult = null;
    });

    try {
      final results = await _recordingController.stopAllRecording();
      final successCount = results.where((r) => r.success).length;
      final totalCount = results.length;
      
      setState(() {
        _lastOperationResult = 'Stopped recording on $successCount/$totalCount cameras';
      });
      
      if (successCount < totalCount) {
        _showOperationResults('Stop Recording Results', results);
      }
    } catch (e) {
      setState(() {
        _lastOperationResult = 'Error: ${e.toString()}';
      });
    } finally {
      setState(() {
        _isOperationInProgress = false;
      });
    }
  }

  void _showOperationResults(String title, List<RecordingResult> results) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(title),
        content: SizedBox(
          width: double.maxFinite,
          child: ListView.builder(
            shrinkWrap: true,
            itemCount: results.length,
            itemBuilder: (context, index) {
              final result = results[index];
              final camera = _cameraManager.getCameraById(result.cameraId);
              return ListTile(
                leading: Icon(
                  result.success ? Icons.check_circle : Icons.error,
                  color: result.success ? Colors.green : Colors.red,
                ),
                title: Text(camera?.fullAddress ?? result.cameraId),
                subtitle: Text(result.error ?? 'Success'),
              );
            },
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  void _showAddCameraDialog() {
    showDialog(
      context: context,
      builder: (context) => AddCameraDialog(
        onAddCamera: _addCamera,
        statusMonitor: _statusMonitor,
      ),
    );
  }

  Future<void> _addCamera(String address) async {
    final parsed = CameraManager.parseAddress(address);
    if (parsed == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Invalid address format')),
      );
      return;
    }

    final success = await _cameraManager.addCamera(
      parsed['ip'],
      port: parsed['port'],
    );

    if (success) {
      setState(() {});
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Camera added successfully')),
      );
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Failed to add camera (duplicate or invalid)')),
      );
    }
  }

  Future<void> _removeCamera(String cameraId) async {
    final success = await _cameraManager.removeCamera(cameraId);
    if (success) {
      setState(() {});
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Camera removed')),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    if (!_isInitialized) {
      return const Scaffold(
        body: Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    final cameras = _cameraManager.cameras;
    final statusSummary = _statusMonitor.getStatusSummary();

    return Scaffold(
      appBar: AppBar(
        title: const Text('Director App'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        actions: [
          IconButton(
            onPressed: () => _statusMonitor.refreshAllStatuses(),
            icon: const Icon(Icons.refresh),
            tooltip: 'Refresh Status',
          ),
        ],
      ),
      body: Column(
        children: [
          // Status Summary
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            color: Colors.grey[100],
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                _buildStatusChip('Total', statusSummary['total']!, Colors.blue),
                _buildStatusChip('Connected', statusSummary['connected']!, Colors.green),
                _buildStatusChip('Recording', statusSummary['recording']!, Colors.red),
                _buildStatusChip('Offline', statusSummary['offline']!, Colors.grey),
              ],
            ),
          ),
          
          // Camera List
          Expanded(
            child: cameras.isEmpty
                ? const Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(Icons.videocam_off, size: 64, color: Colors.grey),
                        SizedBox(height: 16),
                        Text(
                          'No cameras added',
                          style: TextStyle(fontSize: 18, color: Colors.grey),
                        ),
                        SizedBox(height: 8),
                        Text(
                          'Tap the + button to add cameras',
                          style: TextStyle(color: Colors.grey),
                        ),
                      ],
                    ),
                  )
                : ListView.builder(
                    itemCount: cameras.length,
                    itemBuilder: (context, index) {
                      final camera = cameras[index];
                      return CameraListItem(
                        camera: camera,
                        onRemove: () => _removeCamera(camera.id),
                        onRefresh: () => _statusMonitor.refreshCameraStatus(camera.id),
                      );
                    },
                  ),
          ),
          
          // Operation Result
          if (_lastOperationResult != null)
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(8),
              color: Colors.blue[50],
              child: Text(
                _lastOperationResult!,
                textAlign: TextAlign.center,
                style: const TextStyle(color: Colors.blue),
              ),
            ),
          
          // Control Buttons
          Container(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                Expanded(
                  child: ElevatedButton(
                    onPressed: _isOperationInProgress || !_statusMonitor.hasConnectedCameras
                        ? null
                        : _startAllRecording,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                    ),
                    child: _isOperationInProgress
                        ? const SizedBox(
                            height: 20,
                            width: 20,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                            ),
                          )
                        : const Text(
                            'START ALL RECORDING',
                            style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                          ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: ElevatedButton(
                    onPressed: _isOperationInProgress || !_statusMonitor.hasRecordingCameras
                        ? null
                        : _stopAllRecording,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.red,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                    ),
                    child: const Text(
                      'STOP ALL RECORDING',
                      style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _showAddCameraDialog,
        tooltip: 'Add Camera',
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildStatusChip(String label, int count, Color color) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Text(
          count.toString(),
          style: TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: color,
          ),
        ),
      ],
    );
  }

  @override
  void dispose() {
    _statusMonitor.dispose();
    super.dispose();
  }
}
