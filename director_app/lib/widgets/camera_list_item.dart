import 'package:flutter/material.dart';
import '../models/camera_device.dart';

class CameraListItem extends StatelessWidget {
  final CameraDevice camera;
  final VoidCallback onRemove;
  final VoidCallback onRefresh;

  const CameraListItem({
    super.key,
    required this.camera,
    required this.onRemove,
    required this.onRefresh,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      child: ListTile(
        leading: _buildStatusIcon(),
        title: Text(
          camera.fullAddress,
          style: const TextStyle(
            fontWeight: FontWeight.bold,
            fontFamily: 'monospace',
          ),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(_getStatusText()),
            if (camera.lastError != null)
              Text(
                'Error: ${camera.lastError}',
                style: const TextStyle(
                  color: Colors.red,
                  fontSize: 12,
                ),
              ),
            Text(
              'Last seen: ${_formatLastSeen()}',
              style: const TextStyle(fontSize: 12),
            ),
          ],
        ),
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            IconButton(
              onPressed: onRefresh,
              icon: const Icon(Icons.refresh),
              tooltip: 'Refresh Status',
            ),
            IconButton(
              onPressed: () => _showRemoveDialog(context),
              icon: const Icon(Icons.delete),
              tooltip: 'Remove Camera',
            ),
          ],
        ),
        isThreeLine: camera.lastError != null,
      ),
    );
  }

  Widget _buildStatusIcon() {
    switch (camera.status) {
      case CameraStatus.connected:
        return const Icon(
          Icons.videocam,
          color: Colors.green,
          size: 32,
        );
      case CameraStatus.recording:
        return const Icon(
          Icons.fiber_manual_record,
          color: Colors.red,
          size: 32,
        );
      case CameraStatus.offline:
        return const Icon(
          Icons.videocam_off,
          color: Colors.grey,
          size: 32,
        );
    }
  }

  String _getStatusText() {
    switch (camera.status) {
      case CameraStatus.connected:
        return 'Connected';
      case CameraStatus.recording:
        return 'Recording';
      case CameraStatus.offline:
        return 'Offline';
    }
  }

  String _formatLastSeen() {
    final now = DateTime.now();
    final difference = now.difference(camera.lastSeen);
    
    if (difference.inSeconds < 60) {
      return '${difference.inSeconds}s ago';
    } else if (difference.inMinutes < 60) {
      return '${difference.inMinutes}m ago';
    } else if (difference.inHours < 24) {
      return '${difference.inHours}h ago';
    } else {
      return '${difference.inDays}d ago';
    }
  }

  void _showRemoveDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Remove Camera'),
        content: Text('Remove camera ${camera.fullAddress}?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              onRemove();
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Remove'),
          ),
        ],
      ),
    );
  }
}
