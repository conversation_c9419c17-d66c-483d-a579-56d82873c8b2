import 'package:flutter/material.dart';
import '../services/camera_manager.dart';
import '../services/status_monitor.dart';

class AddCameraDialog extends StatefulWidget {
  final Function(String) onAddCamera;
  final StatusMonitor statusMonitor;

  const AddCameraDialog({
    super.key,
    required this.onAddCamera,
    required this.statusMonitor,
  });

  @override
  State<AddCameraDialog> createState() => _AddCameraDialogState();
}

class _AddCameraDialogState extends State<AddCameraDialog> {
  final TextEditingController _addressController = TextEditingController();
  bool _isValidating = false;
  bool _isValid = false;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _addressController.addListener(_validateAddress);
  }

  void _validateAddress() {
    final address = _addressController.text.trim();
    
    if (address.isEmpty) {
      setState(() {
        _isValid = false;
        _errorMessage = null;
      });
      return;
    }

    final isValidFormat = CameraManager.isValidAddress(address);
    setState(() {
      _isValid = isValidFormat;
      _errorMessage = isValidFormat ? null : 'Invalid format (use IP:PORT, e.g., *************:8080)';
    });
  }

  Future<void> _testConnection() async {
    final address = _addressController.text.trim();
    final parsed = CameraManager.parseAddress(address);
    
    if (parsed == null) {
      setState(() {
        _errorMessage = 'Invalid address format';
      });
      return;
    }

    setState(() {
      _isValidating = true;
      _errorMessage = null;
    });

    try {
      final isConnected = await widget.statusMonitor.testCameraConnection(
        parsed['ip'],
        port: parsed['port'],
      );

      setState(() {
        _isValidating = false;
        if (isConnected) {
          _errorMessage = null;
        } else {
          _errorMessage = 'Cannot connect to camera at this address';
        }
      });
    } catch (e) {
      setState(() {
        _isValidating = false;
        _errorMessage = 'Connection test failed: ${e.toString()}';
      });
    }
  }

  void _addCamera() {
    final address = _addressController.text.trim();
    if (_isValid) {
      widget.onAddCamera(address);
      Navigator.of(context).pop();
    }
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Add Camera'),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Enter the camera\'s IP address and port:',
            style: TextStyle(fontSize: 14),
          ),
          const SizedBox(height: 16),
          TextField(
            controller: _addressController,
            decoration: InputDecoration(
              labelText: 'IP Address:Port',
              hintText: '*************:8080',
              border: const OutlineInputBorder(),
              errorText: _errorMessage,
              suffixIcon: _isValidating
                  ? const SizedBox(
                      width: 20,
                      height: 20,
                      child: Padding(
                        padding: EdgeInsets.all(12),
                        child: CircularProgressIndicator(strokeWidth: 2),
                      ),
                    )
                  : _isValid
                      ? IconButton(
                          onPressed: _testConnection,
                          icon: const Icon(Icons.wifi_find),
                          tooltip: 'Test Connection',
                        )
                      : null,
            ),
            keyboardType: TextInputType.url,
            textInputAction: TextInputAction.done,
            onSubmitted: (_) => _addCamera(),
          ),
          const SizedBox(height: 16),
          const Text(
            'Examples:\n'
            '• *************:8080\n'
            '• *********:8080\n'
            '• *********** (uses default port 8080)',
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey,
            ),
          ),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: _isValid && !_isValidating ? _addCamera : null,
          child: const Text('Add Camera'),
        ),
      ],
    );
  }

  @override
  void dispose() {
    _addressController.dispose();
    super.dispose();
  }
}
