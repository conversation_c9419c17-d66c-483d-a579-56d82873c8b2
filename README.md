# Simple Sync Recorder

A Flutter-based two-app mobile system for synchronized video recording across multiple smartphones on a local Wi-Fi network.

## Overview

The system consists of two applications:
- **Camera App** (Slave devices) - Install on 8+ phones for recording
- **Director App** (Master controller) - Install on 1 device for control

## Features

- ✅ Cross-platform (Android + iOS)
- ✅ Local Wi-Fi network operation (no internet required)
- ✅ Sub-second synchronization precision
- ✅ Real-time status monitoring
- ✅ Robust error handling
- ✅ Minimal UI with high contrast
- ✅ Automatic video saving to device storage

## Quick Start

### Prerequisites

1. **Flutter SDK 3.24+** installed
2. **All devices connected to the same Wi-Fi network**
3. **Camera and microphone permissions** granted on all devices

### Installation

1. **Build Camera App:**
   ```bash
   cd camera_app
   flutter pub get
   flutter build apk --release  # For Android
   flutter build ios --release  # For iOS
   ```

2. **Build Director App:**
   ```bash
   cd director_app
   flutter pub get
   flutter build apk --release  # For Android
   flutter build ios --release  # For iOS
   ```

3. **Install on devices:**
   - Install Camera App on 8+ recording devices
   - Install Director App on 1 control device

### Setup Process

1. **Launch Camera Apps** on all recording devices
2. **Note the IP addresses** displayed at the bottom of each Camera App screen
3. **Launch Director App** on the control device
4. **Add cameras** by tapping the "+" button and entering each IP address
5. **Wait for status indicators** to show "Connected"
6. **Start synchronized recording** using the green "START ALL RECORDING" button

## Camera App (Slave Devices)

### UI Elements
- **Large Status Display**: Shows current state (STANDBY/RECORDING/SAVED)
- **Camera Preview**: Small preview window for device positioning
- **Network Info**: Device IP and port at bottom (format: 192.168.1.X:8080)

### States
- **STANDBY**: Default state, ready to receive commands
- **RECORDING**: Active video recording in progress
- **SAVED**: Displayed for 3 seconds after recording stops, then auto-reverts to STANDBY

### HTTP API Endpoints
- `GET /start_record`: Begin video recording
- `GET /stop_record`: End recording and save video
- `GET /status`: Return current status JSON
- `GET /health`: Health check endpoint

### Video Storage
- Videos saved to device's Documents/videos folder
- Android: Also attempts to save to DCIM/CameraApp for gallery access
- iOS: Saved to app's documents directory

## Director App (Master Controller)

### UI Elements
- **Status Summary**: Shows total, connected, recording, and offline camera counts
- **Camera List**: Scrollable list of registered cameras with status badges
- **Control Buttons**: Large START ALL/STOP ALL recording buttons
- **Add Camera Button**: "+" floating action button

### Features
- **Camera Management**: Add/remove cameras with IP validation
- **Real-time Monitoring**: Status polling every 5 seconds
- **Synchronized Commands**: Parallel HTTP requests for true synchronization
- **Error Reporting**: Detailed operation results and error messages

## Network Requirements

- **Local Wi-Fi Network**: All devices must be on the same network
- **Port 8080**: Camera apps listen on this port (configurable)
- **No Internet Required**: System works entirely offline
- **IP Address Range**: Typically 192.168.1.x or 10.0.0.x

## Troubleshooting

### Camera App Issues
- **"Failed to initialize camera"**: Check camera permissions
- **"Error" status**: Restart app and check permissions
- **Network address shows "Unknown"**: Check Wi-Fi connection

### Director App Issues
- **Cameras show "Offline"**: Verify IP addresses and network connectivity
- **"Cannot connect to camera"**: Ensure Camera App is running and on same network
- **Recording fails**: Check that cameras are in "Connected" state first

### Network Issues
- **Devices can't communicate**: Ensure all devices are on same Wi-Fi network
- **IP addresses change**: Note new addresses if devices reconnect to Wi-Fi
- **Firewall blocking**: Some networks may block device-to-device communication

## Testing Strategy

### Single Device Testing
1. Install both apps on one device
2. Use localhost (127.0.0.1:8080) in Director App
3. Test start/stop recording functionality

### Multi-Device Testing
1. Set up 2-3 devices initially
2. Verify network connectivity between devices
3. Test synchronized start/stop commands
4. Gradually add more devices
5. Test error scenarios (disconnect devices, etc.)

### Performance Testing
- Test with maximum number of target devices (8+)
- Measure synchronization precision
- Monitor battery usage during extended recording
- Test network resilience (weak Wi-Fi signals)

## Development Notes

### Key Dependencies
- **camera**: Video recording functionality
- **shelf**: Lightweight HTTP server
- **network_info_plus**: Device IP address detection
- **http**: HTTP client for API calls
- **shared_preferences**: Persistent camera storage

### Architecture
- **Camera App**: HTTP server + camera controller + minimal UI
- **Director App**: HTTP client + camera manager + status monitor + UI

### Synchronization Method
- Uses `Future.wait()` for parallel HTTP requests
- Sub-second precision achieved through concurrent execution
- Error handling ensures partial failures don't block other cameras

## License

This project is provided as-is for educational and development purposes.
