# Project Overview - Simple Sync Recorder

## System Architecture

### High-Level Design
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Camera App    │    │   Camera App    │    │   Camera App    │
│   (Device 1)    │    │   (Device 2)    │    │   (Device N)    │
│                 │    │                 │    │                 │
│ HTTP Server     │    │ HTTP Server     │    │ HTTP Server     │
│ Port 8080       │    │ Port 8080       │    │ Port 8080       │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                    ┌─────────────────┐
                    │  Director App   │
                    │ (Control Device)│
                    │                 │
                    │ HTTP Client     │
                    │ Status Monitor  │
                    │ Camera Manager  │
                    └─────────────────┘
```

### Communication Flow
1. **Discovery**: Director App manually configured with Camera App IP addresses
2. **Status Monitoring**: Director App polls each Camera App every 5 seconds
3. **Command Execution**: Director App sends parallel HTTP requests for synchronized control
4. **Video Storage**: Each Camera App saves videos locally to device storage

## Technical Implementation

### Camera App (Slave Devices)

#### Core Components
- **HTTP Server Service**: Lightweight server using Shelf package
- **Camera Service**: Video recording using Flutter camera package
- **Network Service**: IP address detection and network info
- **UI Screen**: Minimal interface with status display

#### Key Features
- Listens on port 8080 for HTTP commands
- Provides REST API endpoints for control
- Real-time status updates via UI
- Automatic video saving to device storage
- CORS support for cross-origin requests

#### API Endpoints
```
GET /start_record  → Start video recording
GET /stop_record   → Stop recording and save video
GET /status        → Return current status (standby/recording/saved)
GET /health        → Health check endpoint
```

### Director App (Master Controller)

#### Core Components
- **Camera Manager**: Device registration and persistent storage
- **Recording Controller**: Synchronized command execution
- **Status Monitor**: Real-time status polling and updates
- **UI Components**: Camera list, control buttons, status display

#### Key Features
- Camera registration with IP validation
- Parallel HTTP request execution for synchronization
- Real-time status monitoring with 5-second polling
- Persistent storage of camera configurations
- Comprehensive error handling and reporting

## File Structure

### Camera App
```
camera_app/
├── lib/
│   ├── main.dart                    # App entry point
│   ├── screens/
│   │   └── camera_screen.dart       # Main UI screen
│   └── services/
│       ├── http_server.dart         # HTTP server implementation
│       ├── camera_service.dart      # Video recording service
│       └── network_service.dart     # Network utilities
├── android/                         # Android-specific configuration
├── ios/                            # iOS-specific configuration
└── pubspec.yaml                    # Dependencies and metadata
```

### Director App
```
director_app/
├── lib/
│   ├── main.dart                    # App entry point
│   ├── models/
│   │   └── camera_device.dart       # Camera device data model
│   ├── screens/
│   │   └── director_screen.dart     # Main UI screen
│   ├── services/
│   │   ├── camera_manager.dart      # Camera registration and storage
│   │   ├── recording_controller.dart # Synchronized recording control
│   │   └── status_monitor.dart      # Real-time status monitoring
│   └── widgets/
│       ├── camera_list_item.dart    # Camera list item widget
│       └── add_camera_dialog.dart   # Add camera dialog widget
├── android/                         # Android-specific configuration
├── ios/                            # iOS-specific configuration
└── pubspec.yaml                    # Dependencies and metadata
```

## Key Dependencies

### Camera App Dependencies
```yaml
dependencies:
  camera: ^0.10.5+5              # Video recording
  shelf: ^1.4.1                  # HTTP server
  shelf_router: ^1.1.4           # HTTP routing
  network_info_plus: ^4.1.0      # Network information
  path_provider: ^2.1.1          # File system access
  permission_handler: ^11.0.1    # Runtime permissions
```

### Director App Dependencies
```yaml
dependencies:
  http: ^1.1.0                   # HTTP client
  shared_preferences: ^2.2.2     # Persistent storage
  connectivity_plus: ^5.0.1      # Network connectivity
```

## Synchronization Strategy

### Parallel Execution
- Uses `Future.wait()` for concurrent HTTP requests
- All cameras receive commands simultaneously
- Sub-second precision achieved through parallel processing

### Error Handling
- Individual camera failures don't block other cameras
- Detailed error reporting for troubleshooting
- Graceful degradation when some cameras are offline

### Status Management
- Real-time status updates every 5 seconds
- Automatic status transitions (STANDBY → RECORDING → SAVED)
- Network timeout handling with offline detection

## Security Considerations

### Network Security
- Local network operation only (no internet exposure)
- HTTP protocol suitable for trusted local networks
- Port 8080 used for camera communication

### Data Privacy
- Videos stored locally on each device
- No cloud storage or external transmission
- User controls all video data

### Access Control
- Manual camera registration required
- IP address validation
- No automatic device discovery for security

## Performance Characteristics

### Synchronization Precision
- Target: <1 second variance between cameras
- Achieved through parallel HTTP requests
- Network latency is primary limiting factor

### Resource Usage
- Minimal CPU usage when not recording
- Camera service only active during recording
- HTTP server lightweight with minimal memory footprint

### Battery Life
- Optimized for extended recording sessions
- Background services minimized
- Screen brightness can be reduced on camera devices

## Deployment Considerations

### Network Requirements
- All devices must be on same Wi-Fi network
- Router must allow device-to-device communication
- Stable network connection required for synchronization

### Device Requirements
- Android 5.0+ (API level 21) or iOS 12+
- Camera and microphone hardware
- Sufficient storage space for video files
- Wi-Fi connectivity

### Scalability
- Tested with 8+ camera devices
- Performance degrades gracefully with more devices
- Network bandwidth is primary scaling limitation

## Future Enhancements

### Potential Improvements
- Automatic camera discovery via network scanning
- HTTPS support for enhanced security
- Video quality configuration options
- Real-time preview streaming
- Automatic video synchronization and merging
- Cloud storage integration options

### Advanced Features
- GPS synchronization for outdoor recording
- Audio synchronization across devices
- Remote monitoring via web interface
- Automated backup and archival
- Multi-director support for large deployments

## Troubleshooting Quick Reference

### Common Issues
1. **Cameras show offline**: Check network connectivity and IP addresses
2. **Recording fails**: Verify camera permissions and storage space
3. **Poor synchronization**: Check network stability and signal strength
4. **App crashes**: Restart apps and check device resources

### Debug Information
- Camera App displays IP address and port
- Director App shows detailed connection status
- Error messages provide specific failure reasons
- Status monitoring helps identify network issues

## Support and Maintenance

### Regular Maintenance
- Update Flutter dependencies periodically
- Test with latest Android/iOS versions
- Monitor performance with large device counts
- Backup camera configurations

### User Training
- Provide setup documentation to users
- Train on troubleshooting common issues
- Establish support procedures for deployments
- Document specific network configuration requirements
