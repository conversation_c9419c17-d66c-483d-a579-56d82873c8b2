# Testing Guide - Simple Sync Recorder

## Testing Strategy Overview

This guide provides comprehensive testing procedures for the Simple Sync Recorder system, covering unit testing, integration testing, and multi-device deployment testing.

## Unit Testing

### Camera App Tests

#### HTTP Server Tests
```bash
cd camera_app
flutter test test/services/http_server_test.dart
```

**Test Cases:**
- [ ] Server starts successfully on port 8080
- [ ] `/start_record` endpoint returns success
- [ ] `/stop_record` endpoint returns success
- [ ] `/status` endpoint returns correct status
- [ ] CORS headers are properly set
- [ ] Error handling for invalid requests

#### Camera Service Tests
```bash
flutter test test/services/camera_service_test.dart
```

**Test Cases:**
- [ ] Camera initialization with permissions
- [ ] Video recording start/stop functionality
- [ ] File saving to correct directories
- [ ] Permission request handling
- [ ] Error handling for camera failures

#### Network Service Tests
```bash
flutter test test/services/network_service_test.dart
```

**Test Cases:**
- [ ] IP address detection
- [ ] Network info formatting
- [ ] Wi-Fi connectivity checking
- [ ] Error handling for network issues

### Director App Tests

#### Camera Manager Tests
```bash
cd director_app
flutter test test/services/camera_manager_test.dart
```

**Test Cases:**
- [ ] Camera addition with valid IP addresses
- [ ] IP address validation
- [ ] Persistent storage of camera list
- [ ] Camera removal functionality
- [ ] Status updates for cameras

#### Recording Controller Tests
```bash
flutter test test/services/recording_controller_test.dart
```

**Test Cases:**
- [ ] Parallel HTTP requests execution
- [ ] Error handling for failed requests
- [ ] Timeout handling
- [ ] Status updates after operations
- [ ] Operation locking during execution

#### Status Monitor Tests
```bash
flutter test test/services/status_monitor_test.dart
```

**Test Cases:**
- [ ] Periodic status polling
- [ ] Status update callbacks
- [ ] Connection testing
- [ ] Timeout handling
- [ ] Status summary generation

## Integration Testing

### Single Device Integration
**Purpose:** Test both apps on one device using localhost

#### Setup
1. Install both apps on test device
2. Launch Camera App
3. Launch Director App
4. Add camera with address `127.0.0.1:8080`

#### Test Cases
- [ ] Camera appears as "Connected" in Director App
- [ ] Start recording command works
- [ ] Camera status changes to "Recording"
- [ ] Stop recording command works
- [ ] Video file is created and saved
- [ ] Status returns to "Connected"

### Two Device Integration
**Purpose:** Test basic network communication

#### Setup
1. Install Camera App on Device A
2. Install Director App on Device B
3. Connect both devices to same Wi-Fi
4. Add Device A's camera to Director App

#### Test Cases
- [ ] Network discovery works correctly
- [ ] Camera status monitoring functions
- [ ] Synchronized recording commands work
- [ ] Error handling for network issues
- [ ] Status updates in real-time

### Multi-Device Integration
**Purpose:** Test with 3-5 devices to verify scalability

#### Setup
1. Install Camera App on 3-4 devices
2. Install Director App on 1 control device
3. Add all cameras to Director App
4. Verify all show "Connected" status

#### Test Cases
- [ ] All cameras respond to start command simultaneously
- [ ] Synchronization timing is acceptable (<2 seconds)
- [ ] All cameras respond to stop command
- [ ] Status monitoring works for all devices
- [ ] Error handling when some devices fail

## Performance Testing

### Synchronization Precision Test

#### Procedure
1. Set up 8+ camera devices
2. Position cameras to record the same event (e.g., dropping an object)
3. Start synchronized recording
4. Analyze video timestamps to measure sync precision

#### Success Criteria
- [ ] All recordings start within 1 second of each other
- [ ] Video timestamps show <500ms variance
- [ ] No cameras fail to start recording

### Battery Life Test

#### Procedure
1. Fully charge all test devices
2. Start continuous recording session
3. Monitor battery levels every 30 minutes
4. Record when devices reach low battery

#### Success Criteria
- [ ] Devices record for minimum 2 hours continuously
- [ ] Battery drain is consistent across devices
- [ ] No unexpected shutdowns during recording

### Network Resilience Test

#### Procedure
1. Set up recording session with all devices
2. Introduce network stress (move devices, add interference)
3. Test recovery from temporary disconnections
4. Monitor status updates and error handling

#### Success Criteria
- [ ] Devices reconnect automatically after brief disconnections
- [ ] Status monitoring accurately reflects connection state
- [ ] Recording continues after network recovery

## Load Testing

### Maximum Device Test

#### Procedure
1. Set up maximum number of camera devices (10-15)
2. Test synchronized start/stop commands
3. Monitor response times and success rates
4. Check for any performance degradation

#### Success Criteria
- [ ] All devices respond to commands within 5 seconds
- [ ] Success rate >95% for synchronized operations
- [ ] Director App remains responsive with many cameras

### Extended Operation Test

#### Procedure
1. Run system continuously for 4+ hours
2. Perform multiple recording sessions
3. Monitor memory usage and performance
4. Check for any resource leaks

#### Success Criteria
- [ ] No memory leaks in either app
- [ ] Performance remains consistent over time
- [ ] All features continue to work after extended use

## Error Scenario Testing

### Network Failure Scenarios

#### Test Cases
- [ ] Camera device loses Wi-Fi during recording
- [ ] Director device loses Wi-Fi during operation
- [ ] Router restarts during recording session
- [ ] IP addresses change due to DHCP renewal

#### Expected Behavior
- [ ] Graceful error handling with user feedback
- [ ] Automatic reconnection when network recovers
- [ ] No data loss or corruption
- [ ] Clear error messages for troubleshooting

### Device Failure Scenarios

#### Test Cases
- [ ] Camera app crashes during recording
- [ ] Device runs out of storage space
- [ ] Device battery dies during recording
- [ ] Camera hardware failure

#### Expected Behavior
- [ ] Other devices continue recording normally
- [ ] Director app shows appropriate error status
- [ ] Partial recordings are saved when possible
- [ ] System remains stable with failed devices

## Deployment Testing

### Production Environment Test

#### Setup
1. Deploy to actual production devices (8+ phones)
2. Set up in real recording environment
3. Test complete workflow from setup to video retrieval

#### Test Cases
- [ ] Installation process works on all target devices
- [ ] Permission requests are handled correctly
- [ ] Network setup is straightforward
- [ ] Recording quality meets requirements
- [ ] Video files are accessible and playable

### User Acceptance Test

#### Procedure
1. Provide system to end users
2. Train users on setup and operation
3. Observe actual usage scenarios
4. Collect feedback on usability

#### Success Criteria
- [ ] Users can set up system without technical assistance
- [ ] Recording operations are intuitive
- [ ] Error messages are clear and actionable
- [ ] Overall user satisfaction is high

## Automated Testing

### Continuous Integration Setup

#### Test Automation
```bash
# Run all tests
./scripts/run_all_tests.sh

# Generate test coverage report
flutter test --coverage
genhtml coverage/lcov.info -o coverage/html
```

#### Test Scripts
- [ ] Unit test execution
- [ ] Integration test automation
- [ ] Performance benchmarking
- [ ] Code coverage reporting

### Test Data Management

#### Mock Data
- [ ] Simulated camera responses
- [ ] Network condition simulation
- [ ] Error scenario reproduction
- [ ] Performance test data

## Test Reporting

### Test Results Documentation
- [ ] Test execution logs
- [ ] Performance metrics
- [ ] Error rate statistics
- [ ] User feedback summary

### Issue Tracking
- [ ] Bug reports with reproduction steps
- [ ] Performance issues with metrics
- [ ] Feature requests from testing
- [ ] Resolution status tracking
