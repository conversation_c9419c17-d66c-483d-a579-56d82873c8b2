# Setup Guide - Simple Sync Recorder

## Pre-Installation Checklist

### Hardware Requirements
- [ ] 9+ smartphones (8 for recording + 1 for control)
- [ ] All devices support camera and microphone
- [ ] Sufficient storage space on each device (≥2GB recommended)
- [ ] All devices can connect to the same Wi-Fi network

### Software Requirements
- [ ] Flutter SDK 3.24+ installed on development machine
- [ ] Android Studio or Xcode for building apps
- [ ] USB cables for device installation (or wireless deployment setup)

### Network Requirements
- [ ] Stable Wi-Fi network with device-to-device communication enabled
- [ ] Network allows connections on port 8080
- [ ] All devices can obtain IP addresses in the same subnet

## Step-by-Step Installation

### 1. Build the Applications

#### Camera App (for recording devices)
```bash
cd camera_app
flutter clean
flutter pub get
flutter build apk --release  # For Android
# OR
flutter build ios --release  # For iOS
```

#### Director App (for control device)
```bash
cd director_app
flutter clean
flutter pub get
flutter build apk --release  # For Android
# OR
flutter build ios --release  # For iOS
```

### 2. Install on Devices

#### Android Installation
```bash
# Install Camera App on recording devices
adb install camera_app/build/app/outputs/flutter-apk/app-release.apk

# Install Director App on control device
adb install director_app/build/app/outputs/flutter-apk/app-release.apk
```

#### iOS Installation
- Use Xcode to install on connected iOS devices
- Or use wireless deployment if configured

### 3. Device Configuration

#### Recording Devices (Camera App)
1. **Launch Camera App** on each recording device
2. **Grant permissions** when prompted:
   - Camera access
   - Microphone access
   - Storage access
3. **Position devices** using the camera preview
4. **Note the IP address** displayed at bottom of screen
5. **Verify "STANDBY" status** is displayed

#### Control Device (Director App)
1. **Launch Director App** on control device
2. **Add each camera** by tapping the "+" button
3. **Enter IP addresses** noted from Camera Apps (format: 192.168.1.X:8080)
4. **Wait for connection** - status should change to "Connected"
5. **Verify all cameras** are showing "Connected" status

## Network Setup Guide

### Finding Device IP Addresses

#### Method 1: Camera App Display
- IP address is shown at bottom of Camera App screen
- Format: `***********00:8080`

#### Method 2: Router Admin Panel
- Access router admin interface (usually ***********)
- Look for connected devices list
- Match device names to find IP addresses

#### Method 3: Network Scanner Apps
- Use apps like "Network Scanner" or "Fing"
- Scan for devices on port 8080
- Test connectivity to confirm Camera Apps

### Common Network Issues

#### Devices Can't Connect
- **Check Wi-Fi**: Ensure all devices on same network
- **Check Firewall**: Some routers block device-to-device communication
- **Check IP Range**: Devices should be in same subnet (e.g., 192.168.1.x)

#### IP Addresses Keep Changing
- **Router DHCP**: Configure static IP addresses in router
- **Device Settings**: Set static IP in device Wi-Fi settings
- **MAC Address Reservation**: Reserve IP addresses by MAC address

## Testing Procedure

### Phase 1: Single Device Test
1. Install both apps on one device
2. Add camera using `127.0.0.1:8080` in Director App
3. Test start/stop recording
4. Verify video is saved to device storage

### Phase 2: Two Device Test
1. Set up Camera App on Device A
2. Set up Director App on Device B
3. Add Device A's camera to Director App
4. Test synchronized recording
5. Verify both devices show correct status

### Phase 3: Multi-Device Test
1. Add 3-4 more Camera App devices
2. Test synchronized start/stop with all devices
3. Verify synchronization timing (should be <1 second difference)
4. Test error scenarios (disconnect one device during recording)

### Phase 4: Full Scale Test
1. Deploy to all 8+ recording devices
2. Test complete synchronized recording session
3. Monitor for any performance issues
4. Test battery life during extended recording

## Troubleshooting Common Issues

### Camera App Problems

#### "Failed to initialize camera"
- **Solution**: Check camera permissions in device settings
- **Alternative**: Restart app and grant permissions again

#### "Error" status displayed
- **Solution**: Restart Camera App
- **Check**: Ensure no other apps are using camera
- **Verify**: Storage permissions are granted

#### Network address shows "Unknown"
- **Solution**: Check Wi-Fi connection
- **Try**: Disconnect and reconnect to Wi-Fi
- **Verify**: Device has valid IP address

### Director App Problems

#### Cameras show "Offline"
- **Check**: IP addresses are correct
- **Verify**: Camera Apps are running
- **Test**: Ping devices from control device

#### "Cannot connect to camera"
- **Solution**: Verify Camera App is running on target device
- **Check**: Network connectivity between devices
- **Try**: Restart both apps

#### Recording commands fail
- **Ensure**: Cameras are in "Connected" state first
- **Check**: No other recording operations in progress
- **Verify**: Sufficient storage space on recording devices

### Network Problems

#### Devices can't see each other
- **Router Settings**: Enable device-to-device communication
- **Firewall**: Disable AP isolation if enabled
- **Network Type**: Ensure using private network, not guest network

#### Synchronization is poor (>2 second delay)
- **Network**: Check for network congestion
- **Devices**: Ensure devices have good Wi-Fi signal strength
- **Apps**: Restart all apps to clear any delays

## Performance Optimization

### Battery Life
- **Brightness**: Lower screen brightness on recording devices
- **Background Apps**: Close unnecessary apps
- **Power Saving**: Disable power saving modes during recording

### Storage Management
- **Free Space**: Ensure ≥2GB free space per device
- **Video Quality**: Adjust recording quality if needed
- **Cleanup**: Regularly transfer videos off devices

### Network Performance
- **Wi-Fi Channel**: Use less congested Wi-Fi channels
- **Router Position**: Ensure good signal strength to all devices
- **Bandwidth**: Avoid other high-bandwidth activities during recording

## Maintenance

### Regular Tasks
- [ ] Check app updates
- [ ] Verify storage space on all devices
- [ ] Test network connectivity
- [ ] Clean up old video files

### Before Each Recording Session
- [ ] Verify all devices are charged
- [ ] Check Wi-Fi connectivity
- [ ] Test camera functionality
- [ ] Confirm synchronized timing

### After Each Recording Session
- [ ] Transfer videos from devices
- [ ] Check for any error messages
- [ ] Note any performance issues
- [ ] Update device positions if needed
